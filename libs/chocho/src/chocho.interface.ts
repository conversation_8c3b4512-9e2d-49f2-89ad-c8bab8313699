export interface CredpalLoginResponse {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  bnplStatus: string;
  bnplCreditLimit: string;
  createdAt: string;
  credpalStatus: string;
  isNewCustomer: boolean;
}

export interface RetrieveCustomerOrdersRequest {
  userId: string;
  page?: number;
  limit?: number;
  status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
}

export interface RetrieveCustomerOrdersResponse {
  success: boolean;
  orders: {
    id: string;
    parentOrderNumber: string;
    customerId: string;
    storeId: string;
    subtotalAmount: string;
    taxAmount: string;
    shippingAmount: string;
    totalAmount: string;
    paymentStatus: string;
    status: string;
    shippingAddress: object;
    customerInfo: object;
    createdAt: string;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UpdateOrderStatusRequest {
  orderId: string;
  status: string;
  note: string;
}

export interface UpdateOrderStatusResponse {
  success: boolean;
  message: string;
  orderId: string;
  newStatus: string;
}

export interface CreateOrderRequest {
  customerEmail: string;
  storeId: string;
  items: {
    productId: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }[];
  totalAmount: string;
  subtotalAmount: string;
  taxAmount: string;
  shippingAmount: string;
  shippingAddress: {
    fullName: string;
    phone: string;
    address: string;
    email: string;
  };
  paymentMethod: string;
  paymentReference: string;
  paymentStatus: string;
}

export interface CreateOrderResponse {
  success: boolean;
  order: {
    id: string;
    orderNumber: string;
    customerId: string;
    storeId: string;
    totalAmount: string;
    status: string;
    paymentReference: string;
    createdAt: string;
    items: {
      id: string;
      productId: string;
      quantity: number;
      unitPrice: string;
      totalPrice: string;
    }[];
  };
}

export interface FetchCustomerWishlistResponse {
  success: boolean;
  savedItems: {
    id: string;
    customerId: string;
    productId: string;
    createdAt: string;
    product: {
      id: string;
      name: string;
      description: string;
      price: string;
      discountPrice: string;
      stockQuantity: number;
      imageUrl: string;
      slug: string;
      storeId: string;
      categoryId: string;
      brandId: string;
      isActive: boolean;
      isFeatured: boolean;
      reviewRating: number;
      reviewCount: number;
      createdAt: string;
      store: {
        name: string;
        slug: string;
      };
      category: {
        name: string;
      };
    };
  }[];
}

export interface CalculateShippingFeeRequest {
  items: {
    productId: string;
    quantity: number;
  }[];
  address: {
    street: string;
    city: string;
    state: string;
    country?: string;
  };
}

export interface CalculateShippingFeeResponse {
  success: boolean;
  shippingFee: number;
  currency: string;
  formattedFee: string;
  items: {
    productId: string;
    quantity: number;
  }[];
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
  };
}

export interface GetChochoSingleProductResponse {
  id: string;
  storeId: string;
  categoryId: string;
  brandId: string | null;
  returnPolicyId: string | null;
  name: string;
  subtitle: string;
  description: string;
  specifications: string;
  condition: string;
  warrantyMonths: number;
  price: string;
  dollarPrice: string;
  stockQuantity: number;
  imageUrl: string;
  thumbnailUrl: string;
  sortOrder: number;
  status: string;
  isVisible: boolean;
  isSellerFeatured: boolean;
  isSystemFeatured: boolean;
  isBanned: boolean;
  reviewRating: string;
  reviewCount: number;
  slug: string;
  metaTitle: string;
  metaDescription: string;
  createdAt: string;
  updatedAt: string;
}
