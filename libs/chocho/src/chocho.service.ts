import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import {
  CalculateShippingFeeRequest,
  CalculateShippingFeeResponse,
  CredpalLoginResponse,
  CreateOrderRequest,
  CreateOrderResponse,
  FetchCustomerWishlistResponse,
  GetChochoSingleProductResponse,
  RetrieveCustomerOrdersRequest,
  RetrieveCustomerOrdersResponse,
  UpdateOrderStatusRequest,
  UpdateOrderStatusResponse,
} from './chocho.interface';

@Injectable()
export class ChochoService {
  constructor(private readonly httpService: HttpService) {}

  async credpalLogin(email: string): Promise<CredpalLoginResponse> {
    const { data } = await firstValueFrom(
      this.httpService.post(`/credpal/direct-login`, { email }).pipe(
        catchError((error) => {
          console.log(error.response?.data ?? error);

          throw new BadRequestException(
            error.response?.data?.error ?? 'An error occurred',
          );
        }),
      ),
    );

    return data;
  }

  async retrieveCustomerOrders({
    userId,
    page,
    limit,
    status,
  }: RetrieveCustomerOrdersRequest): Promise<RetrieveCustomerOrdersResponse> {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`/external/customers/${userId}/orders`, {
          params: {
            page,
            limit,
            status,
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new BadRequestException(
              error.response?.data?.error ?? 'An error occurred',
            );
          }),
        ),
    );

    return data;
  }

  async updateOrderStatus(
    req: UpdateOrderStatusRequest,
  ): Promise<UpdateOrderStatusResponse> {
    const { data } = await firstValueFrom(
      this.httpService
        .patch(`/external/orders/${req.orderId}/status`, {
          status: req.status,
          note: req.note,
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new BadRequestException(
              error.response?.data?.error ?? 'An error occurred',
            );
          }),
        ),
    );

    return data;
  }

  async createOrder(req: CreateOrderRequest): Promise<CreateOrderResponse> {
    const { data } = await firstValueFrom(
      this.httpService.post(`/external/orders`, req).pipe(
        catchError((error) => {
          console.log(error);

          throw new BadRequestException(
            error.response?.data?.error ?? 'An error occurred',
          );
        }),
      ),
    );

    return data;
  }

  async fetchCustomerWishlist(
    userId: string,
  ): Promise<FetchCustomerWishlistResponse> {
    const { data } = await firstValueFrom(
      this.httpService.get(`/external/customers/${userId}/saved-items`).pipe(
        catchError((error) => {
          console.log(error.response?.data);
          throw new BadRequestException(
            error.response?.data?.error ?? 'An error occurred',
          );

          // throw error.response?.data ?? error;
        }),
      ),
    );

    return data;
  }

  async addToCustomerWishlist(
    userId: string,
    productId: string,
  ): Promise<{ status: boolean; message: string }> {
    const { data } = await firstValueFrom(
      this.httpService
        .post(`/external/customers/${userId}/saved-items`, { productId })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new BadRequestException(
              error.response?.data?.error ?? 'An error occurred',
            );
          }),
        ),
    );

    return data;
  }

  async removeFromCustomerWishlist(
    userId: string,
    productId: string,
  ): Promise<{ status: boolean; message: string }> {
    const { data } = await firstValueFrom(
      this.httpService
        .delete(`/external/customers/${userId}/saved-items/${productId}`)
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new BadRequestException(
              error.response?.data?.error ?? 'An error occurred',
            );
          }),
        ),
    );

    return data;
  }

  async calculateShippingFee(
    req: CalculateShippingFeeRequest,
  ): Promise<CalculateShippingFeeResponse> {
    const { data } = await firstValueFrom(
      this.httpService.post(`/external/shipping/calculate`, req).pipe(
        catchError((error) => {
          console.log(error);

          throw new BadRequestException(
            error.response?.data?.error ?? 'An error occurred',
          );
        }),
      ),
    );

    return data;
  }

  async getChochoSingleProduct(
    productId: string,
  ): Promise<GetChochoSingleProductResponse> {
    const { data } = await firstValueFrom(
      this.httpService.get(`/products/${productId}`).pipe(
        catchError((error) => {
          console.log(error.response?.data);

          throw new BadRequestException(
            error.response?.data ?? 'An error occurred',
          );
        }),
      ),
    );

    return data;
  }

  async getChochoProducts(): Promise<Array<GetChochoSingleProductResponse>> {
    const { data } = await firstValueFrom(
      this.httpService.get(`/products`).pipe(
        catchError((error) => {
          console.log(error);

          throw new BadRequestException(
            error.response?.data ?? 'An error occurred',
          );
        }),
      ),
    );

    return data;
  }
}
