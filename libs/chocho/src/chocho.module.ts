import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';
import { ChochoService } from './chocho.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.chocho.baseUrl,
      headers: {
        'X-API-Key': config.chocho.apiKey,
        'Content-Type': 'application/json',
      },
    }),
  ],
  providers: [ChochoService],
  exports: [ChochoService],
})
export class ChochoModule {}
