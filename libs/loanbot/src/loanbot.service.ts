import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { catchError } from 'rxjs';
import { firstValueFrom } from 'rxjs/internal/firstValueFrom';
import config from 'src/config';

@Injectable()
export class LoanbotService {
  constructor(private readonly httpService: HttpService) {}

  async generateStatement(dto: {
    userId: string;
    accountNumber: string;
    bankCode: string;
    bvn: string;
    phoneNumber: string;
    reference: string;
    employmentType: string;
  }): Promise<{ id: string }> {
    const { data } = await firstValueFrom(
      this.httpService
        .post('/api/statement', dto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data?.data;
  }
}
