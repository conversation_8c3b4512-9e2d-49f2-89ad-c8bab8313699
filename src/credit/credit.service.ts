import {
  PaymentCacheService,
  PaymentTransactionApprovalStatus,
  PaymentTransactionCategory,
  PaymentTransactionStatus,
  PaymentTransactionType,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
  SendNotificationPayload,
} from '@crednet/utils';
import {
  ForbiddenException,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { PersonalCardAccountsRepository } from './repository/personal-card-account.repository';
import { UserPlansRepository } from './repository/user-plan.repository';
import { UserRepository } from '../user/repository/user.repository';
import {
  Events,
  Exchanges,
  NotificationTemplates,
  randomNumberSecure,
} from '../utils';
import { UserProfileRepository } from '../user/repository/user-profile.repository';
import { DocumentsRepository } from './repository/documents.repository';
import { PersonalCardATransactionsRepository } from './repository/personal-card-transactions.repository';
import { PersonalAccountStatementsRepository } from './repository/personal-account-statements.repository';
import { StatementService, STATUS } from './statement.service';
import { PersonalCardTransactions } from '../config/entities/PersonalCardTransactions';
import { PersonalAccountStatements } from '../config/entities/PersonalAccountStatements';
import { DataSource, EntityManager, In } from 'typeorm';
import { PersonalCardAccounts } from '../config/entities/PersonalCardAccounts';
import { AuthData } from '@crednet/authmanager';
import { useDBTransaction } from '../utils/db-transactions';
import { Users } from '../config/entities/Users';
import { ConfigurationRepository } from './repository/configuration.repository';
import { UserPlans } from '../config/entities/UserPlans';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { limits, newPlans } from './limits';
import { PersonalPlansRepository } from './repository/personal-plans.repository';
import { PersonalCardPlans } from 'src/config/entities/PersonalCardPlans';
import { bvns } from 'src/user/users';

/**
 * Service responsible for managing credit card operations and transactions.
 * Handles account management, transaction processing, and credit limit management.
 */
@Injectable()
export class CreditService {
  constructor(
    private readonly userPlansRepository: UserPlansRepository,
    private readonly userRepository: UserRepository,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly transactionRepository: PersonalCardATransactionsRepository,
    private readonly documentsRepository: DocumentsRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly personalAccountStatementsRepository: PersonalAccountStatementsRepository,
    private readonly rmqService: RabbitmqService,
    private readonly dataSource: DataSource,
    private readonly statementService: StatementService,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly configurationRepository: ConfigurationRepository,
    private readonly eventEmitter: EventEmitter2,
    @InjectQueue(Events.SYNC_STATEMENT)
    private readonly syncStatementQueue: Queue,
    private readonly personalPlansRepository: PersonalPlansRepository,
    private readonly profileRepository: UserProfileRepository,
  ) {
    setTimeout(async () => {
      // for (const user of newPlans) {
      //   await this.changePlan(String(user.user_id).replaceAll(',', ''), user.new_plan);
      // }

      // for (const user of bvns) {
      // await  this.preapproveFromLimit(user.toString())

        //   await this.changePlan(String(user.user_id).replaceAll(',', ''), user.new_plan);
      // }

      for (const user of limits) {
        console.log('user', user);
        await this.createCustomerWithCreditLimit(
          String(user.user_id).replaceAll(',', ''),
          parseFloat(String(user.credit_limit).replaceAll(',', '')).toFixed(2),
          // user.loanbot_status,
        );
        //  await this.createCustomerWithCreditLimit(
        //   String(user.bvn).replaceAll(',', ''),
        //   parseFloat(String(user.credit_limit).replaceAll(',', '')).toFixed(2),
        //   user.loanbot_status,
        // );
      }
      // }, 5000);
      //     setTimeout(async () => {
      //       for (const user of [
      // "95504d5b-2f81-40a4-b119-31bd42f7da8e"
      //       ]) {
      //         await this.refundTransaction(user);
      //       }
      // this.syncStatement({userId: '226414', 'statementId': '550710'});
    }, 10000);
  }

  /**
   * Validates if a user can perform transactions on their credit card account.
   * Checks user status, blacklist status, ongoing charges, and account status.
   * @param account - The credit card account to check
   * @param user - The user attempting the transaction
   * @param amount - The amount of the transaction
   * @returns Promise<boolean> - True if the account is valid for transactions
   * @throws UnprocessableEntityException if any validation fails
   */
  async checkLocalAccount(
    account: PersonalCardAccounts,
    user: Users,
  ): Promise<boolean> {
    if (user.userProfiles[0]?.status !== 'activated') {
      throw new UnprocessableEntityException(
        'Your Profile has not been activated',
      );
    }

    if (user.isBlacklisted) {
      throw new UnprocessableEntityException(
        'You are not allowed to perform this transaction, please contact support',
      );
    }

    if (user.pnd && user.pndReason) {
      throw new UnprocessableEntityException(
        `Your account is was restricted due to "${user.pndReason}", please contact support`,
      );
    }

    if (account.ongoingCharge) {
      throw new UnprocessableEntityException(
        'Your account is currently undergoing a repayment',
      );
    }

    if (account.status !== 'active') {
      throw new UnprocessableEntityException('Your account needs to be active');
    }

    return true;
  }

  /**
   * @throws TransactionException
   */
  async newUserSpendingLimit(account: PersonalCardAccounts, amount: number) {
    const newUserSpendingLimit = parseFloat(
      (
        await this.configurationRepository.findOneBy({
          name: 'new_user_spending_limit',
        })
      ).value ?? '0.5',
    );

    const fiftyPercentOfCreditLimit =
      newUserSpendingLimit * parseFloat(account.creditCardLimit);

    const now = new Date();
    const createdAt = new Date(account.createdAt);
    const diffInHours =
      (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60); // Difference in hours

    if (diffInHours < 24) {
      const totalCardTransaction = await this.transactionRepository.sum(
        'amount' as any,
        {
          personalCardAccountsId: account.id,
          status: In([
            PaymentTransactionStatus.SUCCESSFUL,
            PaymentTransactionStatus.PENDING,
          ]),
        },
      );

      const amountPlusTotalTransaction = amount + totalCardTransaction;

      console.log(
        'amountPlusTotalTransaction',
        'newUserSpendingLimit',
        newUserSpendingLimit,
        'diffInHours',
        diffInHours,
        amount,
        totalCardTransaction,
        amountPlusTotalTransaction,
        fiftyPercentOfCreditLimit,
      );

      if (
        amount > fiftyPercentOfCreditLimit ||
        amountPlusTotalTransaction > fiftyPercentOfCreditLimit
      ) {
        throw new UnprocessableEntityException(
          "You're limited to 50% card limit usage for the next 24hrs. Try transacting a lesser amount.",
        );
      }
    }
    return true;
  }

  /**
   * Validates if an account has sufficient balance for a transaction.
   * Checks available balance, credit limit, and minimum balance requirements.
   * @param account - The credit card account to check
   * @param amount - The amount of the transaction
   * @throws UnprocessableEntityException if balance checks fail
   */
  async checkBalances(account: PersonalCardAccounts, amount: number) {
    const availableBalance = account.availableBalance;
    const availableCredit = account.availableCredit;
    const creditCardLimit = account.creditCardLimit;

    const sumOfBalances =
      parseFloat(availableBalance) + parseFloat(availableCredit);
    const limit = parseFloat(creditCardLimit);
    const difference = Math.abs(sumOfBalances - limit);

    if (difference > 10) {
      throw new UnprocessableEntityException(
        'Unbalanced Account, this transaction cannot be completed',
      );
    }

    if (amount > parseFloat(creditCardLimit)) {
      throw new UnprocessableEntityException(
        'Amount is more than your Credit Card Limit',
      );
    }

    if (
      parseFloat(availableCredit) <= amount ||
      parseFloat(availableCredit) - amount < 100
    ) {
      throw new UnprocessableEntityException('Insufficient funds');
    }
  }

  /**
   * Initiates a wallet charge transaction.
   * Handles payment validation, account checks, and transaction creation.
   * @param reference - The unique reference for the transaction
   * @param auth - The authentication data of the user
   * @returns Promise containing the created transaction
   * @throws UnprocessableEntityException if transaction validation fails
   * @throws ForbiddenException if user is not authorized
   */
  async initiateWalletCharge(
    reference: string,
    auth: AuthData,
  ): Promise<PersonalCardTransactions> {
    const payment = await this.paymentCacheService.getPayment(reference);

    if (!payment) {
      throw new UnprocessableEntityException('Invalid transaction');
    }

    if (payment.userId != auth.id + '') {
      throw new ForbiddenException('Invalid transaction');
    }

    if (
      await this.transactionRepository.existsBy({
        transactionReference: reference,
      })
    ) {
      throw new UnprocessableEntityException('Duplicate transaction');
    }

    const account = await this.personalCardAccountsRepository.findOneBy({
      userId: payment.userId,
    });
    if (!account) {
      throw new UnprocessableEntityException('Account not found');
    }

    const user = await this.userRepository.findOne({
      where: {
        id: account.userId,
      },
      relations: ['userProfiles'],
    });

    await this.checkLocalAccount(account, user);
    await this.checkBalances(account, payment.amount);
    await this.newUserSpendingLimit(account, payment.amount);

    let latestStatement =
      await this.personalAccountStatementsRepository.findOne({
        where: {
          userId: payment.userId,
          condition: 'unpaid',
        },
        order: { createdAt: 'DESC' },
      });

    const transaction = await useDBTransaction(
      this.dataSource,
      async (manager) => {
        if (!latestStatement) {
          latestStatement = await this.statementService.generateStatement(
            account,
            user.userProfiles[0],
            manager,
          );
        }

        const tmeta = {
          ...payment.meta,
          pca_balances_snapshot: {
            available_balance: account.availableBalance,
            available_credit: account.availableCredit,
            credit_limit: account.creditCardLimit,
          },
        };

        const transaction = await this.statementService.createTransaction(
          manager,
          latestStatement,
          {
            amount: payment.amount,
            reference: payment.reference,
            category: payment.source,
            description: payment.description,
            type: 'debit',
          },
          tmeta,
        );

        await this.chargeAccount(account, payment.amount, manager);

        await this.finalizeTransaction(
          transaction,
          latestStatement,
          'success',
          manager,
        );

        return transaction;
      },
    );

    this.rmqService.send(Exchanges.PAYMENT, {
      key: payment.returningRoutingKey,
      data: {
        reference: payment.reference,
        transaction: {
          status: PaymentTransactionStatus.SUCCESSFUL,
          amount: payment.amount,
          currency: payment.currency,
          source: payment.source,
          reference: payment.reference,
          walletType: PaymentTransactionWalletType.CREDPAL_CREDIT,
          userId: payment.userId,
          type: PaymentTransactionType.DEBIT,
          approvalStatus: PaymentTransactionApprovalStatus.APPROVED,
          description: payment.description,
          category: PaymentTransactionCategory.TOP_UP,
          meta: payment.meta,
        },
        status: PaymentTransactionStatus.SUCCESSFUL,
        error: null,
      } as QueryTransactionResponseDto,
    });

    return transaction;
  }

  /**
   * Updates the account balance after a charge.
   * @param account - The credit card account to update
   * @param amount - The amount to deduct
   * @param manager - The TypeORM entity manager for database operations
   */
  async chargeAccount(
    account: PersonalCardAccounts,
    amount: number,
    manager: EntityManager,
  ) {
    manager.update(
      PersonalCardAccounts,
      { id: account.id },
      {
        id: account.id,
        availableCredit: parseFloat(account.availableCredit) - amount + '',
        availableBalance: parseFloat(account.availableBalance) + amount + '',
      },
    );
  }

  /**
   * Reverses a charge on an account.
   * @param account - The credit card account to update
   * @param amount - The amount to reverse
   * @param manager - The TypeORM entity manager for database operations
   */
  async reverseChargeAccount(
    account: PersonalCardAccounts,
    amount: number,
    manager: EntityManager,
  ) {
    manager.update(
      PersonalCardAccounts,
      { id: account.id },
      {
        id: account.id,
        availableCredit: parseFloat(account.availableCredit) + amount + '',
        availableBalance: parseFloat(account.availableBalance) - amount + '',
      },
    );
  }

  async refundTransaction(reference: string) {
    console.log('Starting refund', reference);
    const transaction = await this.transactionRepository.findOne({
      where: {
        transactionReference: reference,
      },
      relations: ['statement'],
    });
    if (!transaction || transaction.status != 'success') {
      console.log(
        'Transaction not found or not successful',
        reference,
        transaction?.status,
      );
      return;
    }

    if (
      await this.transactionRepository.exists({
        where: {
          transactionReference: `refund-${reference}`,
        },
      })
    ) {
      console.log('Refund exists', reference);
      return;
    }

    if (
      transaction.category != 'bills' &&
      transaction.category != 'virtual_card' &&
      transaction.category != 'checkout'
    ) {
      throw new Error('Only withdrawal can be refunded');
    }

    const account = await this.personalCardAccountsRepository.findOneBy({
      userId: transaction.userId,
    });

    const user = await this.userRepository.findOne({
      where: { id: transaction.userId },
    });

    const statements = await this.personalAccountStatementsRepository.find({
      where: {
        userId: transaction.userId,
        condition: 'unpaid',
      },
      order: { createdAt: 'DESC' },
    });
    await useDBTransaction(this.dataSource, async (manager) => {
      let latestStatement;
      if (statements.length < 1) {
        latestStatement = await this.statementService.generateStatement(
          account,
          user.userProfiles[0],
          manager,
        );
      } else {
        latestStatement = statements[0];
      }

      await this.finalizeTransaction(
        transaction,
        latestStatement,
        'failed',
        manager,
      );
    });
  }

  public async finalizeTransaction(
    transaction: PersonalCardTransactions,
    latestStatement: PersonalAccountStatements,
    status: 'success' | 'failed',
    manager: EntityManager,
  ) {
    const activeUserPlan = await manager.findOne(UserPlans, {
      where: {
        user: { id: latestStatement.userId },
        status: 'active',
      },
    });

    if (status == 'failed') {
      const transactionStatement =
        transaction.statement ??
        (await manager.findOne(PersonalAccountStatements, {
          where: {
            id: transaction.statementId,
          },
        }));
      // This accounts for the edge case that a statement might be cleared after a pending debit transfer, therefore, add credit
      // also check if the transaction statement is not equal to the latest statement to fully confirm the statement has been cleared
      if (
        transactionStatement?.condition != 'unpaid'
        // transaction.statement.id != latestStatement.id
      ) {
        const interest =
          (activeUserPlan.interest * parseFloat(transaction.amount)) / 100;
        const amount = parseFloat(transaction.amount) + interest;
        await this.statementService.createTransaction(
          manager,
          latestStatement,
          {
            amount,
            reference: `refund-${transaction.transactionReference}`,
            category: 'refund',
            description: 'Credit For Failed Transfer After Statement Close',
            type: 'credit',
          },
        );

        await manager.update(PersonalCardTransactions, transaction.id, {
          status: STATUS.REVERSED,
        });

        this.syncStatementQueue.add(
          Events.SYNC_STATEMENT,
          {
            statementId: latestStatement.id,
            userId: latestStatement.userId,
          },
          {
            delay: 1000,
            removeOnComplete: true,
            removeOnFail: true,
          },
        );
      } else {
        const account = await manager.findOneBy(PersonalCardAccounts, {
          userId: latestStatement.userId,
        });

        await this.reverseChargeAccount(
          account,
          parseFloat(transaction.amount),
          manager,
        );
        await manager.update(PersonalCardTransactions, transaction.id, {
          status: STATUS.REVERSED,
          statementId: null,
        });
        await this.syncStatementQueue.add(
          Events.SYNC_STATEMENT,
          {
            statementId: latestStatement.id,
            userId: latestStatement.userId,
          },
          {
            delay: 1000,
            removeOnComplete: true,
            removeOnFail: true,
          },
        );
      }

      // await this.rmqService.send(Exchanges.NOTIFICATION, {
      //   key: Events.SEND_NOTIFICATION,
      //   data: {
      //     template: NotificationTemplates.TRANSATION_REFUNDED,
      //     userId: latestStatement?.userId,
      //     parameter: {
      //       amount: transaction.amount,
      //     },
      //   } as SendNotificationPayload,
      // });
      this.eventEmitter.emit(Events.SEND_NOTIFICATION, transaction);
    } else {
      await manager.update(PersonalCardTransactions, transaction.id, {
        status: 'success',
      });
      this.eventEmitter.emit(Events.SEND_NOTIFICATION, transaction);

      await this.syncStatementQueue.add(
        Events.SYNC_STATEMENT,
        {
          statementId: latestStatement.id,
          userId: latestStatement.userId,
        },
        {
          delay: 1000,
          removeOnComplete: true,
          removeOnFail: true,
        },
      );
    }
  }

  // async balanceAccounts(page: number) {
  //   const { items, meta } = await this.personalCardAccountsRepository.findMany(
  //     {
  //       limit: 10,
  //       page: page,
  //     },
  //     {
  //       select: ['userId'],
  //       where: {
  //         // status: 'active',
  //       },
  //     },
  //   );

  //   for (const item of items) {
  //     console.log('Balancing account for userId: ', item);
  //     const statemets = await this.personalAccountStatementsRepository.find({
  //       where: {
  //         userId: item.userId,
  //         condition: 'unpaid',
  //       },
  //       order: { createdAt: 'DESC' },
  //     });

  //     if (statemets.length < 1) {
  //       console.log('No open statement for userId: ', item);
  //       // return;
  //     }

  //     if (statemets.length > 1) {
  //       console.log('Multiple open statement for userId: ', item);
  //       continue;
  //     }

  //     const statement = statemets[0];

  //     if (statement?.condition == 'unpaid') {
  //       await this.syncStatement(statement.id, item.userId);
  //     }
  //     // const transactio
  //   }

  //   if (meta.page < meta.totalPages) {
  //     return this.balanceAccounts(meta.page + 1);
  //   }
  // }

  /**
   * Synchronizes statement data including balances and interest.
   * @param payload - Object containing statementId and userId
   * @throws Error if synchronization fails
   */
  @OnEvent(Events.SYNC_STATEMENT)
  async syncStatement(payload: { statementId: string; userId: string }) {
    const { statementId } = payload;

    const { total, interest, aggBalance, carryOverBalance } =
      await this.statementService.totalOutstanding(statementId);

    await this.personalAccountStatementsRepository.update(
      { id: statementId },
      {
        // await this.personalAccountStatementsRepository.update(statementId, {
        carryOverBalance: (carryOverBalance ?? 0) + '',
        aggBalance: aggBalance ? aggBalance + '' : null,
        interest: interest + '',
        totalOutstanding: total + '',
        // payments: payments + '',
      },
    );
  }

  async getPlanFromLimit(limit: number): Promise<PersonalCardPlans> {
    if (limit < 100000) {
      return await this.personalPlansRepository.findOneBy({
        planName: 'Basic',
      });
    }

    if (limit < 400000) {
      return await this.personalPlansRepository.findOneBy({
        planName: 'Standard',
      });
    }

    if (limit < 1000000) {
      return await this.personalPlansRepository.findOneBy({
        planName: 'Premium',
      });
    }

    if (limit < 2500000) {
      return await this.personalPlansRepository.findOneBy({
        planName: 'Prestige',
      });
    }

    return await this.personalPlansRepository.findOneBy({
      planName: 'Platinum',
    });
  }

  async preapproveFromLimit(bvn: string) {
    console.log('Preapproving user with bvn: ', bvn);
    const profile = await this.userProfileRepository.findOneBy({
      bvn,
    });
    if(!profile ||['activated', 'pre-approved'].includes(profile.status) ) return ;
 await this.userRepository.update(
        { id: profile.userId },
        {
          // phoneVerifiedAt: null,
          // status,
          loanbotDecision: 'new_to_bank',
          loanbotStatus: 'new_to_bank',
          status: 'pre-approved',
        },
      );
      await this.userProfileRepository.update(
        { userId: profile.userId },
        { status: 'pre-approved', creditLimit: Math.min(300000, (parseFloat(profile.salary) ?? 0) * .5) },
      );
  }

  /**
   * Creates a new customer with a specified credit limit.
   * @param userId - The unique identifier of the user
   * @param creditLimit - The credit limit to assign to the account
   * @throws Error if account creation fails
   */
  async createCustomerWithCreditLimit(
    bvn: string,
    creditLimit: string,
    loanbotDecision?: string,
  ) {
    const userProfile = await this.userProfileRepository.findOneBy({
      bvn,
    });
    const userId = userProfile?.userId;
    if (!userId) {
      return;
    }
    console.log(
      `Started for userId: ${userId} with credit limit: ${creditLimit}, loanbotDecision: ${loanbotDecision}`,
    );

    const limit = parseFloat(creditLimit);

    const plan = (await this.getPlanFromLimit(limit)) ?? {
      planName: 'Basic',
      interest: 20.0,
      defaultFee: 0.0,
      id: '4',
    };

    const account = await this.personalCardAccountsRepository.findOneBy({
      userId,
    });

    if (account) {
      console.log('User already has account: ', userId);
      // await this.userRepository.update(userId, {
      //   // tag: 'express'
      // });
      if (parseFloat(account.creditCardLimit) > limit) {
        return;
      }
      const spend = parseFloat(account.availableBalance);

      await this.personalCardAccountsRepository.update(
        { id: account.id },
        {
          userId,
          availableBalance: spend + '',
          availableCredit: limit - spend + '',
          creditCardLimit: creditLimit,
          creditCardLimitIncreasedAt: new Date(),
        },
      );
        await this.userProfileRepository.update(
        { userId },
        { creditLimit: limit ? limit : 0 },
      );

       const plans = await this.userPlansRepository.findBy({
        userId, status: 'active'
      });
  
 
      if(plans.filter(p => p.interest <= plan.interest).length) return;
      // create user plan
      if (plans.length) {
        for (const plan of plans) {
          await this.userPlansRepository.update(plan.id, {
            status: 'expired',
          });
        }
      }

      const startDate = new Date();
      const endDate = new Date();
      endDate.setFullYear(startDate.getFullYear() + 1);
      console.log(startDate, endDate);

      await this.userPlansRepository.insert({
        userId,
        originalPlanId: plan.id,
        planName: plan.planName,
        interest: plan.interest,
        fee: plan.defaultFee + '',
        startDate,
        endDate,
        status: 'active',
        duration: 365,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      return;
    }
    return;

    if (
      loanbotDecision == 'auto-approved' ||
      loanbotDecision == 'auto_approved'
    ) {
      if (!limit || limit < 1000) {
        return;
      }

      const plans = await this.userPlansRepository.findBy({
        userId,
      });
      // UPDATE PHONENUMBER verified date to null
      await this.userRepository.update(userId, {
        status: 'processed',
        tag: 'express',
        loanbotDecision: loanbotDecision,
        loanbotStatus: loanbotDecision,
      });

      await this.userProfileRepository.update(
        { userId },
        {
          status: 'activated',
          verifyMethod: 'express',
          creditLimit: parseFloat(creditLimit),
        },
      );

      // await this.documentsRepository.insert({
      //   userId,
      //   type: 'bank_statement',
      //   url: 'express',
      //   filename: 'express',
      //   createdAt: new Date(),
      //   updatedAt: new Date(),
      //   comments: 'auto approved by bot',
      // });

      // create user plan
      if (plans.length) {
        for (const plan of plans) {
          await this.userPlansRepository.update(plan.id, {
            status: 'expired',
          });
        }
      }

      const startDate = new Date();
      const endDate = new Date();
      endDate.setFullYear(startDate.getFullYear() + 1);
      console.log(startDate, endDate);

      await this.userPlansRepository.insert({
        userId,
        originalPlanId: plan.id,
        planName: plan.planName,
        interest: plan.interest,
        fee: plan.defaultFee + '',
        startDate,
        endDate,
        status: 'active',
        duration: 365,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // personal card account with credit limit
      await this.personalCardAccountsRepository.insert({
        userId,
        accountNo: `${userId}${randomNumberSecure().toString()}`,
        availableBalance: '0',
        availableCredit: creditLimit,
        creditCardLimit: creditLimit,
        status: 'active',
        cycleStatus: 'ongoing',
        canCalculateDefault: true,
        pinStatus: 'true',
        repaymentPercentage: '25.00',
        type: 'credit',
        freemium: false,
        freemiumExpireDate: startDate.toISOString(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      console.log(
        `conpleted for userId: ${userId} with credit limit: ${creditLimit}`,
      );
    } else if (loanbotDecision == 'not_approved') {
      console.log('not_approved', userId);
      await this.userRepository.update(
        { id: userId },
        {
          // phoneVerifiedAt: null,
          // status,
          loanbotDecision: loanbotDecision,
          loanbotStatus: loanbotDecision,
          status: 'declined',
        },
      );
      await this.userProfileRepository.update(
        { userId },
        { status: 'declined', creditLimit: limit ? limit : 0 },
      );
    } else if (loanbotDecision == 'new_to_bank') {
      console.log('new_to_bank', userId);
      await this.userRepository.update(
        { id: userId },
        {
          // phoneVerifiedAt: null,
          // status,
          loanbotDecision: loanbotDecision,
          loanbotStatus: loanbotDecision,
          status: 'pre-approved',
        },
      );
      await this.userProfileRepository.update(
        { userId },
        { status: 'pre-approved', creditLimit: limit ? limit : 0 },
      );
    } else {
      let status = loanbotDecision;
      if (loanbotDecision == 'pre_approved') {
        status = 'pre-approved';
      }
      await this.userRepository.update(userId, {
        // phoneVerifiedAt: null,
        // status,
        loanbotDecision: loanbotDecision,
        loanbotStatus: loanbotDecision,
      });

      await this.userProfileRepository.update(
        { userId },
        { status, creditLimit: limit ? limit : 0 },
      );
    }
  }

  async activateCreditCard(userId: string) {
    const account = await this.personalCardAccountsRepository.findOneBy({
      userId,
    });

    if (account) {
      return account;
    }

    const profile = await this.profileRepository.findOneBy({
      userId,
    });

    if (profile?.creditLimit && profile.status == 'activated') {
      await this.createCustomerWithCreditLimit(
        userId,
        profile.creditLimit + '',
      );
    }

    return await this.personalCardAccountsRepository.findOneBy({
      userId,
    });
  }

  /**
   * Creates a new customer with a specified credit limit.
   * @param userId - The unique identifier of the user
   * @param creditLimit - The credit limit to assign to the account
   * @throws Error if account creation fails
   */
  async changePlan(userId: string, planName: string) {
    console.log(`Started for userId: ${userId} with credit limit: ${planName}`);

    const plan = await this.personalPlansRepository.findOneBy({ planName });

    if (!plan) {
      throw new Error('Plan not found');
    }

    const plans = await this.userPlansRepository.findBy({
      userId,
      status: 'active',
    });

    if (
      plans.filter((p) => (p.interest ?? 0) <= (plan.interest ?? 0))?.length
    ) {
      console.log('user already has a lower plan: ', userId);
      return;
    }

    // create user plan
    if (plans.length) {
      for (const plan of plans) {
        await this.userPlansRepository.update(plan.id, {
          // status: 'expired',
            interest: plan.interest,
            planName:  plan.planName,
        });
      }
      return;
    }
    const startDate = new Date();
    const endDate = plans?.length ? plans[0].endDate : new Date();
    if (!plans.length) endDate.setFullYear(startDate.getFullYear() + 1);
    console.log(startDate, endDate);

    await this.userPlansRepository.insert({
      userId,
      originalPlanId: plan.id,
      planName: plan.planName,
      interest: plan.interest,
      fee: plans?.length ? '0' : plan?.defaultFee + '',
      startDate,
      endDate,
      status: 'active',
      duration: 365,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const statements = await this.personalAccountStatementsRepository.findBy({
      userId,
      condition: 'unpaid',
    });

    if (statements.length) {
      this.syncStatement({ userId, statementId: statements[0].id });
    }
  }
}
