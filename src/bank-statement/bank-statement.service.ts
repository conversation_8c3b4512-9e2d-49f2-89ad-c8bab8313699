import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import axios from 'axios';
import config from 'src/config';
import { StatementRepository } from './repositories/statement.repository';
import { AuthData } from '@crednet/authmanager';
import { ConfirmStatementDto, RequestStatementDto } from './dto/statement.dto';
import { In } from 'typeorm';
import { MbsService } from '@app/mbs';
import { PaginationQueryDto } from '@crednet/utils';
import { UserRepository } from 'src/user/repository/user.repository';
import { MbsRepository } from './repositories/mbs.repository';
import { DocumentsRepository } from 'src/credit/repository/documents.repository';
import { VerificationService } from '@app/verification';
import { InjectQueue } from '@nestjs/bullmq';
import { Events } from 'src/utils';
import { Queue } from 'bullmq';
import { randomUUID } from 'crypto';
import { OnEvent } from '@nestjs/event-emitter';
import { LoanbotService } from '@app/loanbot';
import { UserProfileRepository } from 'src/user/repository/user-profile.repository';
import { UserBankStatements } from 'src/config/entities/UserBankStatements';
const mbsBanks = [
  { id: 6, name: 'Access Bank', sortCode: '044' },
  { id: 596, name: 'Accion MF B', sortCode: '0000' },
  { id: 32, name: 'Eco Bank', sortCode: '050' },
  { id: 5, name: 'FCMB', sortCode: '214' },
  { id: 15, name: 'Fidelity Bank', sortCode: '070' },
  { id: 3, name: 'First Bank', sortCode: '011' },
  { id: 44, name: 'FSDH Merchant Bank', sortCode: '400001' },
  { id: 28, name: 'Globus Bank', sortCode: '000027' },
  { id: 13, name: 'GT Bank', sortCode: '058' },
  { id: 38, name: 'Jaiz Bank', sortCode: '301' },
  { id: 4, name: 'Keystone Bank', sortCode: '082' },
  { id: 534, name: 'Kuda Bank', sortCode: '00211' },
  { id: 46, name: 'Lotus Bank', sortCode: '029' },
  { id: 541, name: 'Optimus Bank', sortCode: '0013' },
  { id: 43, name: 'Parallex Bank', sortCode: '********' },
  { id: 2, name: 'Polaris Bank Limited', sortCode: '076' },
  { id: 521, name: 'Premium Trust Bank', sortCode: '000031' },
  { id: 37, name: 'Providus Bank', sortCode: '101' },
  { id: 10, name: 'Stanbic IBTC Bank', sortCode: '221' },
  { id: 1, name: 'Sterling Bank', sortCode: '232' },
  { id: 47, name: 'Titan Trust Bank', sortCode: '922' },
  { id: 14, name: 'UBA ', sortCode: '033' },
  { id: 11, name: 'Union Bank', sortCode: '032' },
  { id: 9, name: 'Unity Bank', sortCode: '215' },
  { id: 95, name: 'VFD MFB', sortCode: '566' },
  { id: 12, name: 'Wema Bank', sortCode: '035' },
  { id: 17, name: 'Zenith Bank', sortCode: '057' },
];
@Injectable()
export class BankStatementService {
  constructor(
    private readonly statementRepository: StatementRepository,
    private readonly userRepository: UserRepository,
    private readonly profileRepository: UserProfileRepository,
    private readonly mbsService: MbsService,
    private readonly mbsRepository: MbsRepository,
    private readonly documentsRepository: DocumentsRepository,
    private readonly verificationService: VerificationService,
    @InjectQueue(Events.SAVE_STATEMENT)
    private readonly saveStatementQueue: Queue,
    private readonly loanbotService: LoanbotService,
  ) {
    // this.getFeedback('********').then(console.log);
    setTimeout(() => {
      // this.retryStatement('119997');
      //   this.confirmRequestStatement(
      //     {
      //       ticketNo: '********-14',
      //       otp: '6082',
      //     },
      //     '616866',
      //   ).then(console.log);
    }, 5000);
  }

  @OnEvent(Events.FETCH_STATEMENT_DOC)
  async saveStatementDOc(data: { id: string; userId: string }) {
    this.saveStatementQueue.add(
      'save-statement',
      {
        userId: data.userId,
        id: data.id,
      },
      {
        lifo: false,
        attempts: 1,
        backoff: { type: 'exponential', delay: 2000 },
        jobId: randomUUID(),
        removeOnComplete: true,
        removeOnFail: false,
        delay: 10000,
      },
    );
  }

  async requestStatement(data: RequestStatementDto, authData: AuthData) {
    let user = await this.userRepository.findOne({
      where: { id: authData.id + '' },
    });

    // const previousStatement = await this.statementRepository.findOne({
    //   where: {
    //     user: { userId: authData.id },
    //     status: In([
    //       Status.StatementRequested,
    //       Status.Success,
    //       Status.RequestConfirmed,
    //     ]),
    //   },
    // });

    // if (previousStatement) {
    //   throw new BadRequestException('You have already requested a statement');
    // }
    const bank = mbsBanks.find((bank) => bank.sortCode === data.bankId);

    const statement = await this.statementRepository.save({
      // destinationId: config.mbs.clientId + '',
      status: 'pending',
      userId: user.id,
      staffName: `${user.name} ${user.lastName}`,
      accountNo: data.accountNo,
      bankName: bank?.id?.toString() ?? data.bankId,
      phone: data.phone,
      verifiedWith: 'mbs',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    //controller
    const response = await this.mbsService.requestStatement({
      ...data,
      bankId: bank?.id?.toString() ?? data.bankId,
      applicantName: statement.staffName,
    });

    if (response.status == '00') {
      const requestId = response.result;
      // await this.getFeedback(
      //   { requestId, statementId: statement.id },
      //   // () => {},
      // );

      await this.statementRepository.update(
        { id: statement.id },
        {
          code: requestId,
          status: 'requested',
        },
      );

      return { requestId: requestId.toString() };
    } else {
      await this.statementRepository.update(
        { id: statement.id },
        {
          status: 'failed',
        },
      );
      throw new BadRequestException(
        'An Error occured, please check your inputs',
      );
    }
  }

  async confirmRequestStatement(data: ConfirmStatementDto, userId: string) {
    //controller
    if (!data.ticketNo || !data.otp) {
      throw new BadRequestException('Ticket number and OTP are required');
    }

    const statement = await this.statementRepository.findOne({
      where: {
        user: { id: userId },
        status: In(['requested']),
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (statement) {
      const mbsRequest = await this.mbsRepository.save({
        ticketId: data.ticketNo,
        password: data.otp,
        accountNumber: statement.accountNo,
        bankCode: statement.bankName,
        phone: statement.phone,
        userId: statement.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      try {
        const response = await this.mbsService.confirmRequestStatement(data);

        const status = response.status;
        console.log(response);
        //     “00” if confirmation succesful

        // mybankStatement® API BOOK FOR INTEGRATION WITH THIRD PARTY ORGANIZATIONS
        // 11/22/2019

        // “01” if request is invalid. E.g. When ticketNo and
        // password do not match or request not due for
        // confirmation
        // “05” Other error encountered
        // “99” No access to call service

        // return response;

        if (status == '00') {
          // update request entity with status, ticket no and password
          await this.statementRepository.update(
            { id: statement.id },
            {
              status: 'confirmed',
            },
          );
          this.saveStatementQueue.add(
            'save-statement',
            {
              ticketNo: data.ticketNo,
              statementId: statement.id,
              userId: statement.userId,
              id: mbsRequest.id,
            },
            {
              lifo: false,
              attempts: 4,
              backoff: { type: 'exponential', delay: 2000 },
              jobId: randomUUID(),
              removeOnComplete: true,
              removeOnFail: false,
              delay: 10000,
            },
          );
          // await this.getFeedback(
          //   { requestId: data.ticketNo, statementId: statement.id },
          //   () =>
          //     this.getPDF({
          //       ticketNo: data.ticketNo,
          //       statementId: statement.id,
          //       destinationId: config.mbs.clientId,
          //       userId: authData.id + '',
          //     }),
          // );
        } else if (status == '01') {
          throw new BadRequestException('Invalid ticket number or password');
        } else {
          // update request entity to failed
          // return response to user
          throw new BadRequestException(
            'An Error occured, please check your ticket number and password',
          );
        }
      } catch (error) {
        throw new BadRequestException(
          'An Error occured, please check your ticket number and password',
        );
      }
    }
  }

  async getPDF(data: {
    ticketNo: string;
    statementId: string;
    destinationId: string;
    userId: string;
  }) {
    //controller
    // get ticketNo using userId, pending request, one pending request per user
    const response = await this.mbsService.getPDF(data.ticketNo);

    const status = response.status;

    if (status == '00') {
      // update request entity with pdf url
      const file = await this.getFileUrl(response.result);
      await this.documentsRepository.insert({
        url: file,
        type: 'bank_statement',
        filename: 'statement.pdf',
        userId: data.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    } else {
      await this.statementRepository.update(
        { id: data.statementId },
        {
          // destinationId: data.destinationId + '',
          status: 'failed',
        },
      );

      throw new BadRequestException(
        'An Error occured, please check your ticket number and password',
      );
    }
  }

  async getFeedback(requestId: string) {
    const response = await this.mbsService.getFeedback(requestId);
    // console.log(response);

    return response;
    // if (response.status == '00') {

    //   // return callback?.();
    // } else {
    //   // await this.statementRepository.update(
    //   //   { id: data.statementId },
    //   //   { status: 'failed' },
    //   // );
    //   // throw new UnprocessableEntityException(response.result.feedback);
    // }
  }

  async getStatements(dto: PaginationQueryDto) {
    const { page, limit } = dto;
    const skip = (page - 1) * limit;

    const [statements, total] = await this.statementRepository.findAndCount({
      skip,
      take: limit,
    });

    return {
      data: statements,
      meta: {
        total,
        page,
        limit,
      },
    };
  }

  async getMyStatements(auth: AuthData, dto: PaginationQueryDto) {
    const { page, limit } = dto;

    const data = await this.statementRepository.findMany(
      { limit, page },
      {
        where: { userId: auth.id + '' },
      },
    );

    return data;
  }

  // New method: Get a specific statement by ID
  async getStatement(auth: AuthData, id: string) {
    const statement = await this.statementRepository.findOne({
      where: { id, userId: auth.id + '' },
    });

    if (!statement) {
      throw new NotFoundException('Statement not found');
    }

    return statement;
  }

  async getBanks() {
    const banks = await this.mbsService.getBanks();

    return banks;
  }

  async getFileUrl(result: any) {
    const base64Data = result.replace(/^data:application\/pdf;base64,/);
    const file = this.base64ToBlob(base64Data);

    const pdf = await this.verificationService.uploadBlob(file);

    return pdf;
  }

  base64ToBlob(base64: string) {
    const binaryString = atob(base64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; ++i) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    return new Blob([bytes], { type: 'application/pdf' });
  }

  async requestStatementNew(data: RequestStatementDto, authData: AuthData) {
    let user = await this.userRepository.findOne({
      where: { id: authData.id + '' },
    });

    let profile = await this.profileRepository.findOne({
      where: { userId: authData.id + '' },
    });

    try {
      const statement = await this.statementRepository.save({
        status: 'pending',
        userId: user.id,
        staffName: `${user.name} ${user.lastName}`,
        accountNo: data.accountNo,
        bankName: data.bankId,
        phone: data.phone,
        verifiedWith: 'mbs',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      //controller
      const response = await this.loanbotService.generateStatement({
        accountNumber: data.accountNo,
        bvn: profile.bvn,
        phoneNumber: data.phone,
        userId: authData.id + '',
        bankCode: data.bankId,
        reference: statement.id,
        employmentType: profile.employmentStatus
      });

      if (response.id) {
        await this.saveStatementData(statement);

        return { requestId: statement.id };
      } else {
        await this.statementRepository.update(
          { id: statement.id },
          {
            status: 'failed',
          },
        );
        throw new BadRequestException(
          'An Error occured, please check your inputs',
        );
      }
    } catch (error) {
      // await this.statementRepository.update(
      //   { id: statement.id },
      //   {
      //     status: 'failed',
      //   },
      // );
      // throw new BadRequestException(
      //   'An Error occured, please check your inputs',
      // );
      throw error;
    }
  }

  async retryStatement(id: string) {
    console.log(' retrying statement');
    try {
      const statement = await this.statementRepository.findOneBy({ id });

      let profile = await this.profileRepository.findOne({
        where: { userId: statement.userId },
      });
      //controller
      const response = await this.loanbotService.generateStatement({
        accountNumber: statement.accountNo,
        bvn: profile.bvn,
        phoneNumber: statement.phone,
        userId: statement.userId,
        bankCode: statement.bankName,
        reference: statement.id,
        employmentType: profile.employmentStatus
      });

      console.log(response);

      if (response.id) {
        await this.saveStatementData(statement);

        return { requestId: statement.id };
      } else {
        await this.statementRepository.update(
          { id: statement.id },
          {
            status: 'failed',
          },
        );
        throw new BadRequestException(
          'An Error occured, please check your inputs',
        );
      }
    } catch (error) {
      console.log(error);
      // await this.statementRepository.update(
      //   { id: statement.id },
      //   {
      //     status: 'failed',
      //   },
      // );
      // throw new BadRequestException(
      //   'An Error occured, please check your inputs',
      // );
      throw error;
    }
  }

  async saveStatementData(statement: UserBankStatements) {
    await this.statementRepository.update(
      { id: statement.id },
      {
        // code: requestId,
        status: 'confirmed',
      },
    );

    await this.documentsRepository.insert({
      url: `http://172.16.0.17:75?statementId=${statement.id}`,
      type: 'bank_statement',
      filename: 'statement.pdf',
      userId: statement.userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
}
