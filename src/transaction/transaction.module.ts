import { Module } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { CashEvents, Exchanges } from 'src/utils';
import config from 'src/config';
import { RabbitmqModule } from '@crednet/utils';
import { TransactionConsumerService } from './consumer/transaction.consumer';
import { PersonalCardATransactionsRepository } from 'src/credit/repository/personal-card-transactions.repository';
import { PaymentRequestEventTypes } from '../utils/events';
@Module({
  imports: [
    RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: 'credit-transaction.queue',
      prefetchCount: 10,
      deadLetterQueueInterval: 100000,
      consumeDeadLetterQueue: config.isCronEnabled,
      showLog: true,
      global: false,
      producer: {
        name: Exchanges.PAYMENT,
        durable: true,
      },
      subscriptions: [
        `${Exchanges.PAYMENT}.${CashEvents.QUERY_TRANSACTION}`,
        `${Exchanges.PAYMENT}.${PaymentRequestEventTypes.FUND_PAYMENT_STATUS}`,
      ],
    }),
  ],
  providers: [
    TransactionService,
    TransactionConsumerService,
    PersonalCardATransactionsRepository,
  ],
})
export class TransactionModule {}
