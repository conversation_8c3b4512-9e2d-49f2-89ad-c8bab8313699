import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Branches } from './Branches';
import { Loans } from './Loans';
import { Merchants } from './Merchants';
import { Users } from './Users';

export enum Status {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  ABANDONED = 'abandoned',
}

@Index('equity_contributions_branch_id_foreign', ['branchId'], {})
@Index('equity_contributions_loan_id_foreign', ['loanId'], {})
@Index('equity_contributions_merchant_id_foreign', ['merchantId'], {})
@Index(
  'equity_contributions_reference_status_index',
  ['reference', 'status'],
  {},
)
@Index('equity_contributions_user_id_foreign', ['userId'], {})
@Entity('equity_contributions')
export class EquityContributions {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'user_id', unsigned: true })
  userId: string;

  @Column('char', { name: 'merchant_id', nullable: true, length: 36 })
  merchantId: string | null;

  @Column('char', {
    name: 'branch_id',
    nullable: true,
    comment: '(DC2Type:guid)',
    length: 36,
  })
  branchId: string | null;

  @Column('bigint', { name: 'loan_id', nullable: true, unsigned: true })
  loanId: string | null;

  @Column('double', { name: 'amount_paid', precision: 30, scale: 2 })
  amountPaid: number;

  @Column('double', { name: 'order_amount', precision: 30, scale: 2 })
  orderAmount: number;

  @Column('varchar', { name: 'reference', nullable: true, length: 255 })
  reference: string | null;

  //WISDOM - PLEASE I ADDED THIS, PLEASE CONFIRM IF YOU ADDED IT TOO!!!
  @Column('varchar', { name: 'order_id', nullable: true, length: 255 })
  orderId: string | null;

  @Column('varchar', { name: 'status', nullable: true, length: 255 })
  status: string | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('double', {
    name: 'equity_amount',
    nullable: true,
    precision: 32,
    scale: 2,
  })
  equityAmount: number | null;

  @Column('varchar', { name: 'equity_percentage', nullable: true, length: 255 })
  equityPercentage: string | null;

  @Column('double', {
    name: 'management_fee_amount',
    nullable: true,
    precision: 32,
    scale: 2,
  })
  managementFeeAmount: number | null;

  @Column('varchar', {
    name: 'management_fee_percentage',
    nullable: true,
    length: 255,
  })
  managementFeePercentage: string | null;

  @Column('datetime', { name: 'reimbursed_at', nullable: true })
  reimbursedAt: Date | null;

  @ManyToOne(() => Branches, (branches) => branches.equityContributions, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'branch_id', referencedColumnName: 'id' }])
  branch: Branches;

  @ManyToOne(() => Loans, (loans) => loans.equityContributions, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'loan_id', referencedColumnName: 'id' }])
  loan: Loans;

  @ManyToOne(() => Merchants, (merchants) => merchants.equityContributions, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'merchant_id', referencedColumnName: 'id' }])
  merchant: Merchants;

  @ManyToOne(() => Users, (users) => users.equityContributions, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: Users;
}
