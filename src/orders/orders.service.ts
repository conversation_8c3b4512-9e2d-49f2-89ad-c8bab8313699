import { BadRequestException, Injectable } from '@nestjs/common';
import { MerchantOrderRepository } from './repositories/orders.repository';
import {
  CheckoutDto,
  MakeFirstEquityPaymentDto,
  GetAmountDto,
  PaymentOptions,
  CalculateShippingFeeDto,
  CreateBillingAddressDto,
} from './dtos/orders.dto';
import { randomUUID } from 'crypto';
import { DataSource, EntityManager } from 'typeorm';
import {
  MerchantOrders,
  MerchantOrdersStatus,
} from '../config/entities/MerchantOrders';
import { CartItems } from '../config/entities/cartItems';
import { Loans } from '../config/entities/Loans';
import { Repayments } from '../config/entities/Repayments';
import { AuthData } from '@crednet/authmanager';
import { EquityContributionsRepository } from './repositories/equity.contributions.repositories';
import { PersonalCardAccountsRepository } from '../credit/repository/personal-card-account.repository';
import {
  EquityContributions,
  Status,
} from '../config/entities/EquityContributions';
import {
  Currency,
  PaymentCacheService,
  PaymentTransactionSource,
  PaymentTransactionWalletType,
  QueryTransactionDto,
} from '@crednet/utils';
import { PaymentRequestEventTypes } from '../utils/events';
import { UserProfileRepository } from '../user/repository/user-profile.repository';
import { ConfigurationRepository } from '../credit/repository/configuration.repository';
import { RabbitmqService } from '@crednet/utils';
import { Exchanges } from '../utils';
import { ChochoService } from '@app/chocho';
import { UserRepository } from '../user/repository/user.repository';
import { BillingAddressRepository } from './repositories/billing-address.repository';

@Injectable()
export class OrdersService {
  constructor(
    private readonly merchantOrderRepository: MerchantOrderRepository,
    private readonly equityContributionsRepository: EquityContributionsRepository,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly usersRepository: UserRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly configurationRepository: ConfigurationRepository,
    private readonly rmqService: RabbitmqService,
    private readonly dataSource: DataSource,
    private readonly chochoService: ChochoService,
    private readonly billingAddressRepository: BillingAddressRepository,
  ) {}

  async checkout(dto: CheckoutDto, auth: AuthData): Promise<any> {
    const userProfile = await this.userProfileRepository.findOne({
      where: { userId: auth.id.toString() },
    });

    if (
      userProfile.status === 'declined' &&
      dto.paymentOption == PaymentOptions.BNPL
    ) {
      throw new BadRequestException(
        'Your profile is not activated for BNPL, please try other payment options',
      );
    }

    this.checkPnd(auth);

    const { totalAmount, namesAndAmount } =
      await this.getAmountAndNameOfChochoProduct(dto);

    const orderId = randomUUID()

    await this.dataSource.transaction(async (manager) => {
      const merchantOrder = await manager.save(MerchantOrders, {
        id: orderId,
        orderNo: randomUUID(),
        userId: auth.id.toString(),
        item: namesAndAmount[0]?.name || '',
        amount: totalAmount,
        status: MerchantOrdersStatus.AWAITING_PAYMENT,
        channel: 'chocho',
        metadata: {
          address: dto.address,
          state: dto.state,
          lga: dto.lga,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const cartItems = namesAndAmount.map((item) => ({
        id: randomUUID(),
        name: item.name,
        quantity: item.quantity,
        price: item.amount,
        status: 'pending',
        merchant: 'Chocho',
        userId: auth.id.toString(),
        merchantOrderId: merchantOrder.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      await manager.save(CartItems, cartItems);
    });
    const payment = await this.makeInitialEquityPaymentForAnOrder(
      {
        amount: totalAmount,
        orderId: orderId,
        isPayingUpfront: dto.paymentOption == PaymentOptions.FULL_PAYMENT,
        hasAccount: false,
        hasGoodCredit: false,
        pin: '',
      },
      auth.id.toString(),
    );

    return payment;
  }

  async getMarchantOrder(orderId: string) {
    return await this.merchantOrderRepository.findOne({
      where: { id: orderId },
      relations: ['user', 'cartItems'],
    });
  }

  async getAmount(userId: string, getAmountDto: GetAmountDto) {
    const orderId = getAmountDto.orderId;

    const order = await this.merchantOrderRepository.findOne({
      where: { id: orderId },
    });

    if (!order) {
      throw new BadRequestException('Order not found');
    }

    //case 1 user wants to pay 100 percent of the amount, the amount is 100 percent of the amount, the type is full payment
    // case 2 user wants to use BNPL and has a good credit, the amount is 40 percent of the amount
    // case 3 user wants to use BNPL and has a bad credit, the amount is 40 percent of the amount + (total price minus avaiable credit)
    // case 4 user is not eligible for cedit yet so we take the default 40 percent of the amount
    let amountToPay = order.amount;
    let isPayingUpfront: boolean = true;
    let hasAccount: boolean = false;
    let hasGoodCredit: boolean = false;

    if (getAmountDto.paymentOption === PaymentOptions.BNPL) {
      isPayingUpfront = false;
      const amountToPayForBNPL = order.amount * 0.4;
      const amountRemainingForCredit = order.amount * 0.6;

      const account = await this.personalCardAccountsRepository.findOne({
        where: { userId: userId },
      });

      if (account) {
        hasAccount = true;
        //todo do i check user profile credit limit too?
        const availableCredit = account.availableCredit;

        if (parseFloat(availableCredit) >= order.amount * 0.6) {
          hasGoodCredit = true;
          amountToPay = amountToPayForBNPL;
        } else {
          amountToPay =
            amountRemainingForCredit -
            parseFloat(availableCredit) +
            amountToPayForBNPL;
        }
      } else {
        amountToPay = amountToPayForBNPL;
      }
      return {
        amount: amountToPay,
        paymentOption: getAmountDto.paymentOption,
        isPayingUpfront,
        hasAccount,
        hasGoodCredit,
      };
    }
    return {
      amount: amountToPay,
      paymentOption: getAmountDto.paymentOption,
      isPayingUpfront,
      hasAccount,
      hasGoodCredit,
    };
  }

  async makeInitialEquityPaymentForAnOrder(
    dto: MakeFirstEquityPaymentDto,
    userId: string,
  ) {
    // this.checkPnd(auth);

    const order = await this.merchantOrderRepository.findOne({
      where: { id: dto.orderId },
    });

    order.metadata.isPayingUpfront = dto.isPayingUpfront;
    order.metadata.hasAccount = dto.hasAccount;
    order.metadata.hasGoodCredit = dto.hasGoodCredit;

    await this.merchantOrderRepository.update(
      { id: dto.orderId },
      { metadata: order.metadata },
    );

    const equityPayment = {
      userId,
      amount: dto.amount,
      status: Status.PENDING,
      reference: randomUUID(),
      orderId: order.id,
      orderAmount: order.amount,
      equityAmount: dto.amount,
      equityPercentage: (
        ((order.amount - dto.amount) / order.amount) *
        100
      ).toString(),
    };

    const equity = await this.equityContributionsRepository.save(equityPayment);

    return this.initiatePayment(equity, dto);
  }

  private async initiatePayment(
    equity: EquityContributions,
    dto: MakeFirstEquityPaymentDto,
  ) {
    await this.paymentCacheService.savePayment({
      source: PaymentTransactionSource.STOCKS_SERVICE, //Todo: change source later
      userId: equity.userId,
      reference: equity.reference,
      walletType: PaymentTransactionWalletType.CREDPAL_CASH,
      currency: Currency.NGN,
      amount: equity.equityAmount,
      description: 'Equity Contribution',
      returningRoutingKey: PaymentRequestEventTypes.FUND_PAYMENT_STATUS,
      meta: {
        reference: equity.reference,
        amount: equity.equityAmount,
        isPayingUpfront: dto.isPayingUpfront,
        hasAccount: dto.hasAccount,
        hasGoodCredit: dto.hasGoodCredit,
      },
    });

    await this.equityContributionsRepository.update(
      { reference: equity.reference },
      { status: Status.PROCESSING },
    );

    return equity;
  }

  async finalisePayment(equity: EquityContributions) {
    return await this.dataSource.transaction(async (manager) => {
      await manager.update(
        EquityContributions,
        { id: equity.id },
        { status: Status.SUCCESS },
      );

      const order = await manager.findOne(MerchantOrders, {
        where: { id: equity.orderId },
      });

      if (order.metadata?.isPayingUpfront) {
        await manager.update(
          MerchantOrders,
          { id: equity.orderId },
          { status: MerchantOrdersStatus.APPROVED },
        );
      } else {
        if (order.metadata?.hasAccount) {
          const configuration = await this.configurationRepository.findOne({
            where: { name: 'days_before_payment' },
          });
          const allowedNumberOfDaysToPermitTheFirstMonThToBeFirstRepaymnt =
            parseInt(configuration.value);

          await this.createLoanAndRepayments(
            equity,
            allowedNumberOfDaysToPermitTheFirstMonThToBeFirstRepaymnt,
            manager,
          );
        } else {
          await manager.update(
            MerchantOrders,
            { id: equity.orderId },
            { status: MerchantOrdersStatus.PENDING },
          );
        }
      }

      return order;
    });
  }

  async getOrder(orderId: string) {
    return await this.merchantOrderRepository.findOne({
      where: { id: orderId },
      relations: ['user', 'cartItems'],
    });
  }

  async updateOrderStatusToUnderReview(orderId: string) {
    const getOrder = await this.getOrder(orderId);

    if (
      getOrder.status === MerchantOrdersStatus.PENDING ||
      getOrder.status === MerchantOrdersStatus.REQUEUE
    ) {
      await this.merchantOrderRepository.update(
        { id: orderId },
        { status: MerchantOrdersStatus.UNDER_REVIEW },
      );
    } else {
      throw new BadRequestException(
        'An Order can only be sent to be under review if it is pending or requeued',
      );
    }
  }

  async updateOrderStatusToApproved(orderId: string) {
    await this.merchantOrderRepository.update(
      { id: orderId },
      { status: MerchantOrdersStatus.APPROVED },
    );
  }

  async updateOrderStatusToBeRequeued(orderId: string) {
    const getOrder = await this.getOrder(orderId);

    if (getOrder.status === MerchantOrdersStatus.UNDER_REVIEW) {
      await this.merchantOrderRepository.update(
        { id: orderId },
        { status: MerchantOrdersStatus.REQUEUE },
      );
    } else {
      throw new BadRequestException(
        'An Order can only be requeued if it is under review',
      );
    }
  }

  private checkPnd(auth: AuthData) {
    if (auth.pnd == 1) {
      throw new BadRequestException(
        'You are not allowed to complete this operation, please contact support',
      );
    }
  }

  /**
   * Creates a loan record and associated repayments for a user
   * @param equity - The equity contribution record
   * @param allowedNumberOfDaysToPermitTheFirstMonThToBeFirstRepaymnt - Configuration value for days before payment
   * @param manager - Transaction manager for database operations
   */
  private async createLoanAndRepayments(
    equity: EquityContributions,
    allowedNumberOfDaysToPermitTheFirstMonThToBeFirstRepaymnt: number,
    manager: EntityManager,
  ) {
    const loanAmount = equity.orderAmount - equity.equityAmount;

    const loan = await manager.save(Loans, {
      userId: equity.userId,
      loanAmount: loanAmount.toString(),
      status: 'disbursed',
      tenure: 3,
      disbursedAt: new Date(),
      requestedAmount: loanAmount,
    });

    const currentDate = new Date();
    const currentDayOfMonth = currentDate.getDate();
    const repaymentAmount = loanAmount / 3;

    let firstRepaymentDate: Date;
    if (
      allowedNumberOfDaysToPermitTheFirstMonThToBeFirstRepaymnt +
        currentDayOfMonth <
      25
    ) {
      firstRepaymentDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        25,
      );
    } else {
      firstRepaymentDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() + 1,
        25,
      );
    }

    const repayments = [];
    for (let i = 0; i < 3; i++) {
      const dueDate = new Date(firstRepaymentDate);
      dueDate.setMonth(firstRepaymentDate.getMonth() + i);

      const repayment = {
        userId: equity.userId,
        loanId: loan.id,
        amount: repaymentAmount,
        status: 'not_due',
        dueDate: dueDate.toISOString().split('T')[0],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      repayments.push(repayment);
    }

    await manager.save(Repayments, repayments);

    return { loan, repayments };
  }

  async requeryPendingPayment(page: number) {
    console.log('running job:: requeryPendingPayment ', page);
    const items = await this.equityContributionsRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {
          status: Status.PROCESSING,
        },
      },
    );

    for (const item of items.items) {
      this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentRequestEventTypes.QUERY_TRANSACTION,
        data: {
          reference: item.reference,
          returningRoutingKey: PaymentRequestEventTypes.FUND_PAYMENT_STATUS,
        } as QueryTransactionDto,
      });
    }

    if (page < items.meta.totalPages) {
      return this.requeryPendingPayment(++page);
    }

    return items;
  }

  async getOrAddChochoUser(auth: AuthData) {
    const user = await this.usersRepository.findOne({
      where: { id: auth.id.toString() },
    });

    if (user.credpalId !== null) {
      return {
        choChoId: user.credpalId,
        email: user.email,
        firstName: user?.name,
        lastName: user?.lastName,
        phone: user?.phoneNo,
      };
    }

    const chochoUser = await this.chochoService.credpalLogin(user.email);

    user.credpalId = chochoUser.id;

    await this.usersRepository.save(user);

    return {
      choChoId: chochoUser.id,
      email: chochoUser.email,
      firstName: chochoUser?.firstName,
      lastName: chochoUser?.lastName,
      phone: chochoUser?.phoneNumber,
    };
  }

  async addWishListItem(auth: AuthData, productId: string) {
    const user = await this.getOrAddChochoUser(auth);
    const userId = user.choChoId;
    return await this.chochoService.addToCustomerWishlist(userId, productId);
  }

  async fetchWishlist(auth: AuthData) {
    const user = await this.getOrAddChochoUser(auth);
    const userId = user.choChoId;
    return await this.chochoService.fetchCustomerWishlist(userId);
  }

  async removeWishListItem(auth: AuthData, productId: string) {
    const user = await this.getOrAddChochoUser(auth);
    const userId = user.choChoId;
    return await this.chochoService.removeFromCustomerWishlist(
      userId,
      productId,
    );
  }

  async calculateShippingFee(dto: CalculateShippingFeeDto) {
    return await this.chochoService.calculateShippingFee(dto);
  }

  async createBillingAddress(dto: CreateBillingAddressDto, userId: string) {
    const billingAddress = {
      user_id: userId,
      ...dto,
    };
    return await this.billingAddressRepository.save({
      ...billingAddress,
      created_at: new Date(),
      updated_at: new Date(),
    });
  }

  async updateBillingAddress(
    dto: CreateBillingAddressDto,
    id: string,
    userId: string,
  ) {
    const billingAddress = {
      user_id: userId,
      ...dto,
    };

    await this.billingAddressRepository.update(id, {
      ...billingAddress,
      updated_at: new Date(),
    });
    return await this.billingAddressRepository.findOneBy({ id: +id });
  }

  async getBillingAddresses(userId: string) {
    return await this.billingAddressRepository.find({
      where: { user_id: userId },
      order: { created_at: 'DESC' },
    });
  }

  private async getAmountAndNameOfChochoProduct(dto: CheckoutDto): Promise<{
    totalAmount: number;
    namesAndAmount: Array<{ name: string; quantity: number; amount: number }>;
  }> {
    let totalAmount = 0;
    const namesAndAmount = [];

    for (const item of dto.cartItems) {
      const chcohoProduct = await this.chochoService.getChochoSingleProduct(
        item.productId,
      );

      totalAmount += +chcohoProduct.price * item.quantity;
      namesAndAmount.push({
        name: chcohoProduct.name,
        quantity: item.quantity,
        amount: +chcohoProduct.price,
      });
    }

    return { totalAmount, namesAndAmount };
  }
}
