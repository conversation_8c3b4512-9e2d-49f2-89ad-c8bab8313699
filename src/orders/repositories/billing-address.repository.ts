import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { BillingAddress } from '../../config/entities/BillingAddress';

@Injectable()
export class BillingAddressRepository extends TypeOrmRepository<BillingAddress> {
  constructor(private readonly dataSource: DataSource) {
    super(BillingAddress, dataSource.createEntityManager());
  }
}
