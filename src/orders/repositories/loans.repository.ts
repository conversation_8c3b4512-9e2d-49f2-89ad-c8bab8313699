import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { Loans } from '../../config/entities/Loans';

@Injectable()
export class LoansRepository extends TypeOrmRepository<Loans> {
  constructor(private readonly dataSource: DataSource) {
    super(Loans, dataSource.createEntityManager());
  }
}
