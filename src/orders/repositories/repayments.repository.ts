import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { Repayments } from '../../config/entities/Repayments';

@Injectable()
export class RepaymentsRepository extends TypeOrmRepository<Repayments> {
  constructor(private readonly dataSource: DataSource) {
    super(Repayments, dataSource.createEntityManager());
  }
}
