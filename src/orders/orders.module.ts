import { Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { MerchantOrderRepository } from './repositories/orders.repository';
import { CartItemsRepository } from './repositories/cart.items.repository';
import { EquityContributionsRepository } from './repositories/equity.contributions.repositories';
import { PersonalCardAccountsRepository } from '../credit/repository/personal-card-account.repository';
import { UserProfileRepository } from '../user/repository/user-profile.repository';
import { ConfigurationRepository } from '../credit/repository/configuration.repository';
import { OrderPaymentsConsumer } from './order.payments.consumer';
import { PaymentCacheModule } from '@crednet/utils';
import { ChochoModule } from '@app/chocho';
import { UserRepository } from '../user/repository/user.repository';
import { BillingAddressRepository } from './repositories/billing-address.repository';

@Module({
  imports: [PaymentCacheModule, ChochoModule],
  controllers: [OrdersController],
  providers: [
    OrdersService,
    MerchantOrderRepository,
    EquityContributionsRepository,
    PersonalCardAccountsRepository,
    CartItemsRepository,
    UserRepository,
    UserProfileRepository,
    ConfigurationRepository,
    OrderPaymentsConsumer,
    BillingAddressRepository,
  ],
})
export class OrdersModule {}
