import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  ValidateNested,
  IsNumber,
  Length,
  IsEnum,
  IsBoolean,
  IsOptional,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class CartItemDto {
  @ApiProperty()
  @IsString()
  productId: string;

  @ApiProperty()
  @IsNumber()
  quantity: number;

  // @ApiProperty()
  // @IsString()
  // status: string;

  // @ApiProperty()
  // @IsString()
  // merchant: string;
}

export enum PaymentOptions {
  FULL_PAYMENT = 'full-payment',
  BNPL = 'bnpl',
}

export class CheckoutDto {
  @ApiProperty({ type: [CartItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemDto)
  cartItems: CartItemDto[];

  @ApiProperty({ enum: PaymentOptions })
  @IsEnum(PaymentOptions)
  paymentOption: PaymentOptions;

  @ApiProperty()
  @IsString()
  address: string;

  @ApiProperty()
  @IsString()
  state: string;

  @ApiProperty()
  @IsString()
  lga: string;

  @ApiPropertyOptional()
  @IsOptional()
  postalCode?: string;
}

export class MakeFirstEquityPaymentDto {
  @ApiProperty()
  @IsString()
  orderId: string;

  @ApiProperty()
  @IsBoolean()
  isPayingUpfront: boolean;

  @ApiProperty()
  @IsBoolean()
  hasAccount: boolean;

  @ApiProperty()
  @IsBoolean()
  hasGoodCredit: boolean;

  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty()
  @IsString()
  @Length(4, 4)
  pin: string;
}

export class GetAmountDto {
  @ApiProperty()
  @IsString()
  orderId: string;

  @ApiProperty({ enum: PaymentOptions })
  @IsEnum(PaymentOptions)
  paymentOption: PaymentOptions;
}

export class ShippingItemDto {
  @ApiProperty()
  @IsString()
  productId: string;

  @ApiProperty()
  @IsNumber()
  quantity: number;
}

export class ShippingAddressDto {
  @ApiProperty()
  @IsString()
  street: string;

  @ApiProperty()
  @IsString()
  city: string;

  @ApiProperty()
  @IsString()
  state: string;

  @ApiProperty({ default: 'Nigeria' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value || 'Nigeria')
  country?: string = 'Nigeria';
}

export class CalculateShippingFeeDto {
  @ApiProperty({ type: [ShippingItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShippingItemDto)
  items: ShippingItemDto[];

  @ApiProperty({ type: ShippingAddressDto })
  @ValidateNested()
  @Type(() => ShippingAddressDto)
  address: ShippingAddressDto;
}

export class CreateBillingAddressDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  first_name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  last_name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  address_line1?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  address_line2?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  postal_code?: string;

  @ApiProperty({ default: 'Nigeria' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value || 'Nigeria')
  country?: string = 'Nigeria';
}
