import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
} from '@crednet/utils';
import { OrdersService } from './orders.service';
import { Exchanges } from '../utils';
import { PaymentRequestEventTypes } from '../utils/events';
import {
  EquityContributions,
  Status,
} from '../config/entities/EquityContributions';
import { EquityContributionsRepository } from './repositories/equity.contributions.repositories';

@Injectable()
export class OrderPaymentsConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly ordersService: OrdersService,
    private readonly equityContributionsRepository: EquityContributionsRepository,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${PaymentRequestEventTypes.FUND_PAYMENT_STATUS}`,
      async ({ data, ack, reject }) => {
        try {
          if (data?.status) {
            await this.handlePaymentStatus(data, ack, reject);
          } else {
            console.log('Meta not found');
            ack();
            return;
          }
        } catch (e) {
          console.log(e);
          //force the error to be thrown so that rabbitmq can nack it in the rabbitmq service
          throw e;
        }
      },
    );
  }

  async handlePaymentStatus(
    payload: QueryTransactionResponseDto,
    ack: () => void,
    reject: () => void,
  ) {
    try {
      const { reference, status } = payload;
      const data = await this.equityContributionsRepository.findOne({
        where: { reference },
      });

      if (
        (data?.status != Status.SUCCESS && data?.status != Status.FAILED) ||
        status == PaymentTransactionStatus.REVERSED
      ) {
        switch (status) {
          case PaymentTransactionStatus.SUCCESSFUL:
            this.handleSuccess(data);
            break;

          case PaymentTransactionStatus.FAILED:
            this.handleFailed(data);
            break;

          case PaymentTransactionStatus.REVERSED:
            // this.handleRefunded(data);
            break;

          case PaymentTransactionStatus.NOT_FOUND:
            if (
              data.status == Status.PENDING ||
              data.status == Status.PROCESSING
            ) {
              this.handleNotFound(data, payload.transaction);
            }
            break;

          default:
            break;
        }
      }
      ack();
    } catch (e) {
      console.log(e);
      if (String(e?.message).includes('already exist')) {
        ack();
        return;
      }

      reject();
    }
  }

  private async handleSuccess(data: EquityContributions) {
    await this.ordersService.finalisePayment(data);
  }

  private async handleFailed(data: EquityContributions) {
    const update = {
      status: Status.FAILED,
    };
    await this.equityContributionsRepository.update({ id: data.id }, update);
  }

  // private async handleRefunded(data: EquityContributions) {
  //   const update = { isRefunded: true };
  //   if (data.status == Status.PENDING || data.status == Status.PROCESSING) {
  //     update['status'] = Status.FAILED;
  //   }

  //   await this.equityContributionsRepository.update({ id: data.id }, update);
  // }

  private async handleNotFound(
    data: EquityContributions,
    transaction: PaymentTransaction,
  ) {
    const billAge = Date.now() - data.createdAt.getTime();
    const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds
    if (
      transaction.walletType == PaymentTransactionWalletType.CREDPAL_CASH &&
      transaction.status == PaymentTransactionStatus.NOT_FOUND
    ) {
      if (billAge >= tenMinutesInMs) {
        await this.equityContributionsRepository.update(
          { id: data.id },
          {
            status: Status.ABANDONED,
          },
        );
      }
    }
  }
}
