import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth } from '@nestjs/swagger';
import {
  CheckoutDto,
  GetAmountDto,
  MakeFirstEquityPaymentDto,
  CalculateShippingFeeDto,
  CreateBillingAddressDto,
} from './dtos/orders.dto';

@Controller('orders')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post('/checkout')
  checkout(
    @Body() dto: CheckoutDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.ordersService.checkout(dto, auth);
  }

  @Get('/get-amount')
  getAmount(
    @GetAuthData() auth: AuthData,
    @Query() getAmountDto: GetAmountDto,
  ): Promise<any> {
    return this.ordersService.getAmount(auth.id + '', getAmountDto);
  }

  @Post('/make-initial-equity-payment')
  @PinRequirement('pin')
  makeInitialEquityPayment(
    @Body() dto: MakeFirstEquityPaymentDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.ordersService.makeInitialEquityPaymentForAnOrder(
      dto,
      auth.id.toString(),
    );
  }

  @Get('/order/:orderId')
  getOrder(@Param('orderId') orderId: string): Promise<any> {
    return this.ordersService.getOrder(orderId);
  }

  @Post('/order/under-review/:orderId')
  updateOrderStatusToUnderReview(
    @Param('orderId') orderId: string,
  ): Promise<any> {
    return this.ordersService.updateOrderStatusToUnderReview(orderId);
  }

  @Post('/order/approved/:orderId')
  updateOrderStatusToApproved(@Param('orderId') orderId: string): Promise<any> {
    return this.ordersService.updateOrderStatusToApproved(orderId);
  }

  @Post('/order/requeue/:orderId')
  updateOrderStatusToBeRequeued(
    @Param('orderId') orderId: string,
  ): Promise<any> {
    return this.ordersService.updateOrderStatusToBeRequeued(orderId);
  }

  @Get('/chocho/user')
  getOrAddChochoUser(@GetAuthData() auth: AuthData) {
    return this.ordersService.getOrAddChochoUser(auth);
  }

  @Post('/chocho/wishlist/:productId')
  addWishListItem(
    @GetAuthData() auth: AuthData,
    @Param('productId') productId: string,
  ) {
    return this.ordersService.addWishListItem(auth, productId);
  }

  @Get('/chocho/wishlist')
  fetchWishlist(@GetAuthData() auth: AuthData) {
    return this.ordersService.fetchWishlist(auth);
  }

  @Delete('/chocho/wishlist/:productId')
  removeWishListItem(
    @GetAuthData() auth: AuthData,
    @Param('productId') productId: string,
  ) {
    return this.ordersService.removeWishListItem(auth, productId);
  }

  @Post('/calculate-shipping-fee')
  calculateShippingFee(@Body() dto: CalculateShippingFeeDto) {
    return this.ordersService.calculateShippingFee(dto);
  }

  @Post('/billing-address')
  createBillingAddress(
    @Body() dto: CreateBillingAddressDto,
    @GetAuthData() auth: AuthData,
  ) {
    return this.ordersService.createBillingAddress(dto, auth.id.toString());
  }

  @Put('/billing-address/:id')
  updateBillingAddress(
    @Body() dto: CreateBillingAddressDto,
    @GetAuthData() auth: AuthData,
    @Param('id') id,
  ) {
    return this.ordersService.updateBillingAddress(dto, id, auth.id.toString());
  }

  @Get('/billing-address')
  getBillingAddresses(@GetAuthData() auth: AuthData) {
    return this.ordersService.getBillingAddresses(auth.id.toString());
  }
}
